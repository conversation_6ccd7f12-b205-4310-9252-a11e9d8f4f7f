#!/bin/bash

# Funkcja do generowania losowego 6-znakowego hash
generate_hash() {
    # Użyj $RANDOM i date dla lepszej kompatybilności
    local chars="abcdefghijklmnopqrstuvwxyz0123456789"
    local hash=""
    for i in {1..6}; do
        local random_index=$((RANDOM % ${#chars}))
        hash="${hash}${chars:$random_index:1}"
    done
    echo "$hash"
}

# Funkcja do wyciągnięcia nazwy podstrony z ścieżki
get_page_name() {
    local file_path="$1"

    # Sprawdź czy to plik z folderu components
    if echo "$file_path" | grep -q "components/"; then
        # Dla plików z components, wyciągnij ścieżkę po components/
        local path_after_components=$(echo "$file_path" | sed 's|.*/components/||')
        # Usuń nazwę pliku
        local dir_path=$(dirname "$path_after_components")
        # Zamień / na _
        local component_path=$(echo "$dir_path" | sed 's|/|_|g')

        # <PERSON><PERSON><PERSON> to root folder components, użyj tylko "components"
        if [ "$component_path" = "." ]; then
            echo "components"
        else
            echo "components_${component_path}"
        fi
    else
        # Dla plików z app, zachowaj poprzednią logikę
        # Usuń część przed /app/
        local path_after_app=$(echo "$file_path" | sed 's|.*/app/||')
        # Usuń nazwę pliku
        local dir_path=$(dirname "$path_after_app")
        # Usuń nawiasy z nazw folderów
        local clean_path=$(echo "$dir_path" | sed 's/[()]//g')
        # Zamień / na _
        local page_name=$(echo "$clean_path" | sed 's|/|_|g')

        # Jeśli to root folder, użyj "root"
        if [ "$page_name" = "." ]; then
            echo "root"
        else
            echo "$page_name"
        fi
    fi
}

# Funkcja do wyciągnięcia nazwy komponentu z nazwy pliku
get_component_name() {
    local file_name="$1"
    local base_name=$(basename "$file_name" .tsx)
    base_name=$(basename "$base_name" .jsx)
    
    if [ "$base_name" = "page" ]; then
        echo "Page"
    else
        echo "$base_name"
    fi
}

# Funkcja do przetwarzania pojedynczego pliku
process_file() {
    local file_path="$1"
    echo "Przetwarzam plik: $file_path"
    
    local file_name=$(basename "$file_path")
    local component_name=$(get_component_name "$file_name")
    local page_name=$(get_page_name "$file_path")
    
    local temp_file=$(mktemp)
    local modified=false
    
    while IFS= read -r line; do
        local new_line="$line"
        
        # Sprawdź komponenty Button (które nie mają już id)
        if echo "$line" | grep -qE '<Button[A-Za-z]*[[:space:]]' && ! echo "$line" | grep -qE '\bid[[:space:]]*='; then
            local component_type=$(echo "$line" | sed -n 's/.*<\(Button[A-Za-z]*\).*/\1/p')
            if [ ! -z "$component_type" ]; then
                local hash=$(generate_hash)
                local id="${page_name}_${component_name}_button_${hash}"
                echo "  DEBUG: page_name='$page_name', component_name='$component_name', hash='$hash'"
                echo "  DEBUG: Generated ID: '$id'"
                new_line=$(echo "$line" | sed "s/<${component_type}/<${component_type} id=\"${id}\"/")
                echo "  Dodano ID do $component_type: $id"
                modified=true
            fi
        fi

        # Sprawdź zwykłe elementy <button (które nie mają już id)
        if echo "$line" | grep -qE '<button[[:space:]]' && ! echo "$line" | grep -qE '\bid[[:space:]]*=' && ! echo "$line" | grep -qE '<Button[A-Za-z]*[[:space:]]'; then
            local hash=$(generate_hash)
            local id="${page_name}_${component_name}_button_${hash}"
            echo "  DEBUG: page_name='$page_name', component_name='$component_name', hash='$hash'"
            echo "  DEBUG: Generated ID: '$id'"
            new_line=$(echo "$line" | sed "s/<button/<button id=\"${id}\"/")
            echo "  Dodano ID do button: $id"
            modified=true
        fi
        
        # Sprawdź komponenty Link (które nie mają już id) - tylko jeśli nie znaleziono Button
        if ! echo "$line" | grep -qE '<Button[A-Za-z]*[[:space:]]' && ! echo "$line" | grep -qE '<button[[:space:]]' && echo "$line" | grep -qE '<Link[A-Za-z]*[[:space:]]' && ! echo "$line" | grep -qE '\bid[[:space:]]*='; then
            local component_type=$(echo "$line" | sed -n 's/.*<\(Link[A-Za-z]*\).*/\1/p')
            if [ ! -z "$component_type" ]; then
                local hash=$(generate_hash)
                local id="${page_name}_${component_name}_link_${hash}"
                echo "  DEBUG: page_name='$page_name', component_name='$component_name', hash='$hash'"
                echo "  DEBUG: Generated ID: '$id'"
                new_line=$(echo "$line" | sed "s/<${component_type}/<${component_type} id=\"${id}\"/")
                echo "  Dodano ID do $component_type: $id"
                modified=true
            fi
        fi

        # Sprawdź zwykłe elementy <a (które nie mają już id) - tylko jeśli nie znaleziono innych
        if ! echo "$line" | grep -qE '<Button[A-Za-z]*[[:space:]]' && ! echo "$line" | grep -qE '<button[[:space:]]' && ! echo "$line" | grep -qE '<Link[A-Za-z]*[[:space:]]' && echo "$line" | grep -qE '<a[[:space:]]' && ! echo "$line" | grep -qE '\bid[[:space:]]*='; then
            local hash=$(generate_hash)
            local id="${page_name}_${component_name}_link_${hash}"
            echo "  DEBUG: page_name='$page_name', component_name='$component_name', hash='$hash'"
            echo "  DEBUG: Generated ID: '$id'"
            new_line=$(echo "$line" | sed "s/<a/<a id=\"${id}\"/")
            echo "  Dodano ID do a: $id"
            modified=true
        fi
        
        echo "$new_line" >> "$temp_file"
    done < "$file_path"
    
    if [ "$modified" = true ]; then
        mv "$temp_file" "$file_path"
        echo "  ✅ Plik został zmodyfikowany"
        return 0
    else
        rm "$temp_file"
        echo "  ⏭️  Brak komponentów do modyfikacji"
        return 1
    fi
}

# Główna funkcja
main() {
    local app_dir="./app"
    local components_dir="./components"

    # Sprawdź czy foldery istnieją
    if [ ! -d "$app_dir" ] && [ ! -d "$components_dir" ]; then
        echo "❌ Ani folder /app ani /components nie istnieją!"
        exit 1
    fi

    echo "🚀 Rozpoczynam dodawanie ID do komponentów Button, Link, button i a..."

    # Przygotuj listę folderów do przeszukania
    local search_dirs=""
    if [ -d "$app_dir" ]; then
        search_dirs="$app_dir"
        echo "📁 Przeszukuję folder: $app_dir"
    fi
    if [ -d "$components_dir" ]; then
        if [ ! -z "$search_dirs" ]; then
            search_dirs="$search_dirs $components_dir"
        else
            search_dirs="$components_dir"
        fi
        echo "📁 Przeszukuję folder: $components_dir"
    fi

    # Znajdź wszystkie pliki .tsx i .jsx w obu folderach
    local tsx_files=$(find $search_dirs -name "*.tsx" -o -name "*.jsx" | grep -v node_modules)
    local file_count=$(echo "$tsx_files" | wc -l)

    echo "📄 Znaleziono $file_count plików .tsx/.jsx"
    
    local modified_count=0
    
    # Przetwórz każdy plik
    while IFS= read -r file; do
        if [ ! -z "$file" ]; then
            if process_file "$file"; then
                ((modified_count++))
            fi
        fi
    done <<< "$tsx_files"
    
    echo ""
    echo "✨ Zakończono!"
    echo "📊 Zmodyfikowano $modified_count z $file_count plików"
}

# Uruchom skrypt
main "$@"
