#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do generowania losowego 6-znakowego hash
function generateRandomHash() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Funkcja do konwersji nazwy pliku na format ID
function formatFileName(fileName) {
    return fileName
        .replace(/\.tsx?$/, '') // usuń rozszerzenie
        .replace(/([A-Z])/g, '$1') // zachowaj camelCase
        .replace(/^./, str => str.toLowerCase()); // pierwsza litera mała
}

// Funkcja do wyciągnięcia nazwy komponentu z nazwy pliku
function getComponentName(fileName) {
    const baseName = fileName.replace(/\.tsx?$/, '');
    // Je<PERSON><PERSON> to page.tsx, użyj nazwy folderu
    if (baseName === 'page') {
        return 'Page';
    }
    return baseName;
}

// Funkcja do wyciągnięcia nazwy podstrony z ścieżki
function getPageName(filePath) {
    const parts = filePath.split('/');
    // Znajdź część po 'app/'
    const appIndex = parts.findIndex(part => part === 'app');
    if (appIndex === -1) return 'unknown';
    
    const pathAfterApp = parts.slice(appIndex + 1);
    
    // Usuń nawiasy z nazw folderów (np. (auth) -> auth)
    const cleanPath = pathAfterApp.map(part => part.replace(/[()]/g, ''));
    
    // Jeśli ostatni element to page.tsx, usuń go
    if (cleanPath[cleanPath.length - 1] === 'page') {
        cleanPath.pop();
    }
    
    // Połącz ścieżkę
    return cleanPath.join('_') || 'root';
}

// Funkcja do przetwarzania pojedynczego pliku
function processFile(filePath) {
    console.log(`Przetwarzam plik: ${filePath}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const fileName = path.basename(filePath);
    const componentName = getComponentName(fileName);
    const pageName = getPageName(filePath);
    
    let modified = false;
    const modifiedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Regex do znajdowania komponentów Button i Link (wielkość liter ma znaczenie)
        const buttonRegex = /<(Button[A-Za-z]*)\s+(?![^>]*\sid=)/;
        const linkRegex = /<(Link[A-Za-z]*)\s+(?![^>]*\sid=)/;
        
        let newLine = line;
        
        // Sprawdź Button komponenty
        const buttonMatch = line.match(buttonRegex);
        if (buttonMatch) {
            const componentType = buttonMatch[1];
            const hash = generateRandomHash();
            const id = `${pageName}_${componentName}_button_${hash}`;
            
            // Dodaj id jako pierwszy atrybut
            newLine = line.replace(
                `<${componentType}`,
                `<${componentType} id="${id}"`
            );
            
            console.log(`  Dodano ID do ${componentType}: ${id}`);
            modified = true;
        }
        
        // Sprawdź Link komponenty (tylko jeśli nie znaleziono Button)
        if (!buttonMatch) {
            const linkMatch = line.match(linkRegex);
            if (linkMatch) {
                const componentType = linkMatch[1];
                const hash = generateRandomHash();
                const id = `${pageName}_${componentName}_link_${hash}`;
                
                // Dodaj id jako pierwszy atrybut
                newLine = line.replace(
                    `<${componentType}`,
                    `<${componentType} id="${id}"`
                );
                
                console.log(`  Dodano ID do ${componentType}: ${id}`);
                modified = true;
            }
        }
        
        modifiedLines.push(newLine);
    }
    
    if (modified) {
        fs.writeFileSync(filePath, modifiedLines.join('\n'));
        console.log(`  ✅ Plik został zmodyfikowany`);
        return true;
    } else {
        console.log(`  ⏭️  Brak komponentów do modyfikacji`);
        return false;
    }
}

// Funkcja do rekurencyjnego przeszukiwania folderów
function findTsxFiles(dir) {
    const files = [];
    
    function traverse(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Pomiń node_modules i inne foldery systemowe
                if (!item.startsWith('.') && item !== 'node_modules') {
                    traverse(fullPath);
                }
            } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.jsx'))) {
                files.push(fullPath);
            }
        }
    }
    
    traverse(dir);
    return files;
}

// Główna funkcja
function main() {
    const appDir = path.join(process.cwd(), 'app');
    
    if (!fs.existsSync(appDir)) {
        console.error('❌ Folder /app nie istnieje!');
        process.exit(1);
    }
    
    console.log('🚀 Rozpoczynam dodawanie ID do komponentów Button i Link...');
    console.log(`📁 Przeszukuję folder: ${appDir}`);
    
    const tsxFiles = findTsxFiles(appDir);
    console.log(`📄 Znaleziono ${tsxFiles.length} plików .tsx/.jsx`);
    
    let modifiedCount = 0;
    
    for (const file of tsxFiles) {
        if (processFile(file)) {
            modifiedCount++;
        }
    }
    
    console.log('\n✨ Zakończono!');
    console.log(`📊 Zmodyfikowano ${modifiedCount} z ${tsxFiles.length} plików`);
}

// Uruchom skrypt
if (require.main === module) {
    main();
}
