'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { useConfirmEmail } from '@/hooks/useConfirmEmailForm';
import { ConfirmEmailFormInputsTypes } from '@/validation/confirmEmailValidation';
import { handleResendEmailConfirmation } from '@/api/auth';
import { ItemBox } from '@/components/ItemBox';

const RESEND_TIMEOUT = 60; // seconds

const ConfirmEmail = () => {
    const t = useTranslations('confirm_email');
    const searchParams = useSearchParams();

    const [secondsLeft, setSecondsLeft] = useState(0);
    const [isResending, setIsResending] = useState(false);

    const uuid = searchParams.get('uuid');
    const token = searchParams.get('token');

    const {
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        control,
        handleFormSubmit,
        defaultValues,
    } = useConfirmEmail({ uuid, token });

    const onResend = async () => {
        if (!uuid) return;
        setIsResending(true);
        try {
            await handleResendEmailConfirmation(uuid);
            setSecondsLeft(RESEND_TIMEOUT);
        } finally {
            setIsResending(false);
        }
    };

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: ConfirmEmailFormInputsTypes,
        control,
        defaultValues,
    });

    return (
        <ItemBox>
            <div className="w-box-100 h-box-100 px-box-100 justify-evenly flex flex-col py-box-50">
                <h1 className="text-2xl font-bold text-left">{t('title')}</h1>
                <>
                    <form onSubmit={handleSubmit(handleFormSubmit)} className="flex flex-col gap-4" noValidate>
                        {fieldsToRender}
                        <ButtonGradient id="auth_confirm-email_Page_button_itm28n" type="submit" disabled={!isValid || isSubmitting} isLoading={isSubmitting}>
                            {t('button')}
                        </ButtonGradient>
                        <ButtonGradient
                            type="button"
                            onClick={onResend}
                            disabled={isResending || secondsLeft > 0}
                            isLoading={isResending}
                            className="mt-2 text-sm text-left underline text-main-100"
                        >
                            {secondsLeft > 0
                                ? `To send new email wait ${secondsLeft} seconds`
                                : 'Resend confirmation e-mail'}
                        </ButtonGradient>
                    </form>
                </>
            </div>
        </ItemBox>
    );
};

export default ConfirmEmail;
