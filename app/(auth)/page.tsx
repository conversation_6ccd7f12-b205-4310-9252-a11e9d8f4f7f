'use client';

import { ItemBox } from '@/components/ItemBox';
import { ButtonGradient } from '@/components/Buttons';
import { useLoginForm } from '@/hooks/useLoginForm';
import { useTranslations } from 'next-intl';
import { renderFormFields } from '@/common/renderFormFields';
import { LoginFormDataInputsTypes } from '@/validation/loginFormValidation';
import { RouterPaths } from '@/common/routerPaths';
import Link from 'next/link';

const LoginScreen = () => {
    const t = useTranslations('login_screen');
    const { register, handleSubmit, errors, handleFormSubmit, trigger, isValid, formFields, isSubmitting } =
        useLoginForm();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: LoginFormDataInputsTypes,
        trigger,
    });

    return (
        <ItemBox>
            <div className="flex flex-col justify-center gap-6 items-center w-box-100 h-box-100 px-box-100">
                <p className="text-2xl w-full text-start">{t('title')}</p>
                <form className="flex flex-col w-full gap-4" onSubmit={handleSubmit(handleFormSubmit)}>
                    {fieldsToRender}
                    <Link id="auth_Page_link_aau" href={RouterPaths.REQUEST_PASSWORD_RESET} className="text-sm text-end text-main-100">
                        {t('request_password_reset')}
                    </Link>
                    <ButtonGradient id="auth_Page_button_" type="submit" disabled={!isValid} isLoading={isSubmitting}>
                        {t('button')}
                    </ButtonGradient>
                </form>
                <div className="flex flex-row gap-1 text-sm self-start w-full">
                    <p>{t('not_have')}</p>
                    <Link id="auth_Page_link_" href={RouterPaths.SIGNUP} className="text-main-100 underline">
                        {t('sign_up')}
                    </Link>
                </div>
            </div>
        </ItemBox>
    );
};
export default LoginScreen;
