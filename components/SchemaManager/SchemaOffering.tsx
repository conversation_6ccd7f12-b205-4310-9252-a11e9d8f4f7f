import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { SchemaOfferingNavbar } from './components/SchemaBuilderNavbar';
import { DeploymentType } from '@/types/deployments';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useGetAndSetSchemasOfIssuer } from '@/hooks/useGetAndSetSchemasOfIssuer';

const TYPE = DeploymentType.ISSUER;

export const SchemaOffering = () => {
    const { activeVersionId, setStep } = useSchemaBuilderContext();
    const { data } = useDeploymentDetails({ type: TYPE });
    const { data: schemas = [], refetch } = useGetAndSetSchemasOfIssuer({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });

    const schema = schemas.find(s => s.id === activeVersionId);
    if (!schema) throw new Error('No schema found');

    return (
        <div>
            <SchemaOfferingNavbar onBackButtonClick={() => setStep(SchemaBuilderStepEnum.SCHEMA_LIST)} />
            {JSON.stringify(schema)}
            Schema Offering: {activeVersionId}
        </div>
    );
};
