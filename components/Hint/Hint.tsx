'use client';
import { useEffect, useId, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import clsx from 'clsx';
import { useHintConfig } from '@/contexts/HintContext';
import type { Placement } from '@/contexts/HintContext';
import { HintBubble } from './HintBubble';
import { useAutoPosition } from '@/hooks/useAutoPosition';

type Props = {
    id: string;
    className?: string;
    iconSize?: number;
    placement?: Placement; // default from provider
    offset?: number; // default from provider
};

export function Hint({ id, className, iconSize = 18, placement, offset }: Props) {
    const {
        hintsMap,
        placement: defaultPlacement,
        offset: defaultOffset,
        closeOnBlur,
        closeOnOutsideClick,
    } = useHintConfig();
    const content = hintsMap[id];
    const [open, setOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const bubbleRef = useRef<HTMLDivElement>(null);
    const tooltipId = useId();

    const {
        coords,
        placement: actualPlacement,
        recompute,
    } = useAutoPosition(buttonRef, bubbleRef, placement ?? defaultPlacement ?? 'auto', offset ?? defaultOffset ?? 8);

    // Close on outside click
    useEffect(() => {
        if (!open || !closeOnOutsideClick) return;
        const onPointerDown = (e: MouseEvent) => {
            const target = e.target as Node | null;
            if (target && !buttonRef.current?.contains(target) && !bubbleRef.current?.contains(target)) {
                setOpen(false);
            }
        };
        window.addEventListener('mousedown', onPointerDown);
        return () => window.removeEventListener('mousedown', onPointerDown);
    }, [open, closeOnOutsideClick]);

    // Close on blur
    useEffect(() => {
        if (!open || !closeOnBlur) return;
        const btn = buttonRef.current;
        const onBlur = (e: FocusEvent) => {
            const next = e.relatedTarget as Node | null;
            if (next && (btn?.contains(next) || bubbleRef.current?.contains(next))) return;
            setOpen(false);
        };
        btn?.addEventListener('blur', onBlur);
        return () => btn?.removeEventListener('blur', onBlur);
    }, [open, closeOnBlur]);

    // Close on Esc
    useEffect(() => {
        if (!open) return;
        const onKey = (e: KeyboardEvent) => {
            if (e.key === 'Escape') setOpen(false);
        };
        window.addEventListener('keydown', onKey);
        return () => window.removeEventListener('keydown', onKey);
    }, [open]);

    // Recompute when open
    useEffect(() => {
        if (open) recompute();
    }, [open, recompute]);

    if (!content) {
        if (process.env.NODE_ENV !== 'production') {
            console.warn(`[Hint] Missing content for id=\"${id}\"`);
        }
    }

    return (
        <>
            <button
                ref={buttonRef}
                type="button"
                aria-describedby={open ? tooltipId : undefined}
                aria-expanded={open}
                aria-haspopup="dialog"
                onClick={() => setOpen(v => !v)}
                className={clsx(
                    'inline-flex items-center justify-center rounded-full bg-transparent text-main-600 hover:text-main-100 transition-colors',
                    className
                )}
                style={{ width: iconSize + 6, height: iconSize + 6 }}
            >
                <span className="sr-only">Show help</span>
                <svg
                    width={iconSize}
                    height={iconSize}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" />
                    <path
                        d="M9.75 9.5a2.25 2.25 0 1 1 3.874 1.5c-.53.498-1.124.902-1.624 1.5-.25.301-.5.699-.5 1.5"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                    />
                    <circle cx="12" cy="17" r="1" fill="currentColor" />
                </svg>
            </button>

            {open &&
                typeof window !== 'undefined' &&
                content &&
                createPortal(
                    <HintBubble
                        ref={bubbleRef}
                        title={content.title}
                        description={content.description}
                        placement={actualPlacement}
                        style={{ top: coords.top, left: coords.left }}
                    />,
                    document.body
                )}
        </>
    );
}
