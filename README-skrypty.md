# Skrypty do automatycznego dodawania ID do komponentów Button i Link

## Opis

Te skrypty automatycznie dodają unikalne ID do wszystkich komponentów `<Button` i `<Link` w folderze `/app` według wzorca:

```
id="nazwa_podstrony_nazwaKomponentu_button/link_randomowyHash6znakowy"
```

## Dostępne skrypty

### 1. Skrypt Node.js (zalecany)
- **Plik**: `add-ids-to-components.js`
- **Wymagania**: Node.js
- **Uruchomienie**: `node add-ids-to-components.js`

### 2. Skrypt Bash
- **Plik**: `add-ids-to-components.sh`
- **Wymagania**: Bash (Linux/macOS)
- **Uruchomienie**: `./add-ids-to-components.sh`

## Funkcjonalność

### Co robią skrypty:
1. Przeszukują wszystkie pliki `.tsx` i `.jsx` w folderze `/app`
2. Znajdują komponenty zaczynające się od `<Button` lub `<Link`
3. **Sprawdzają czy komponent już ma atrybut `id` - jeśli tak, pomijają go**
4. Dodają ID według wzorca, jeśli jeszcze go nie ma
5. Generują losowy 6-znakowy hash dla każdego komponentu

### Przykład transformacji:

**Przed:**
```jsx
<ButtonGradient type="submit" disabled={!isValid} isLoading={isSubmitting}>
    {t('button')}
</ButtonGradient>

<Link href={RouterPaths.SIGNUP} className="text-main-100 underline">
    {t('sign_up')}
</Link>
```

**Po:**
```jsx
<ButtonGradient id="auth_LoginScreen_button_weaw13" type="submit" disabled={!isValid} isLoading={isSubmitting}>
    {t('button')}
</ButtonGradient>

<Link id="auth_LoginScreen_link_x9k2m1" href={RouterPaths.SIGNUP} className="text-main-100 underline">
    {t('sign_up')}
</Link>
```

### Wzorzec ID:
- `nazwa_podstrony`: np. `auth`, `dashboard_profile`, `dashboard_new_issuer`
- `nazwaKomponentu`: np. `LoginScreen`, `Page`, `ProfilePage`
- `button/link`: typ komponentu
- `randomowyHash`: 6-znakowy losowy hash (a-z, 0-9)

## Bezpieczeństwo

✅ **Skrypty są bezpieczne:**
- Nie modyfikują komponentów, które już mają atrybut `id`
- Tworzą kopie zapasowe przed modyfikacją
- Wyświetlają szczegółowe logi o zmianach
- Nie usuwają ani nie nadpisują istniejących atrybutów

## Uruchomienie

### Node.js:
```bash
node add-ids-to-components.js
```

### Bash:
```bash
chmod +x add-ids-to-components.sh
./add-ids-to-components.sh
```

## Przykładowy output:
```
🚀 Rozpoczynam dodawanie ID do komponentów Button i Link...
📁 Przeszukuję folder: ./app
📄 Znaleziono 15 plików .tsx/.jsx

Przetwarzam plik: app/(auth)/page.tsx
  Dodano ID do ButtonGradient: auth_LoginScreen_button_weaw13
  Dodano ID do Link: auth_LoginScreen_link_x9k2m1
  ✅ Plik został zmodyfikowany

Przetwarzam plik: app/dashboard/page.tsx
  ⏭️  Brak komponentów do modyfikacji

✨ Zakończono!
📊 Zmodyfikowano 8 z 15 plików
```

## Obsługiwane komponenty

Skrypty rozpoznają wszystkie komponenty zaczynające się od:
- `<Button` (np. `ButtonGradient`, `ButtonBorder`, `ButtonSmall`)
- `<Link` (np. `Link`, `LinkGradient`, `LinkBorder`)

Wielkość liter ma znaczenie - komponenty muszą zaczynać się wielką literą.
